from flask_restx import Namespace, Resource, fields
from flask import request, current_app, send_from_directory
from app.models.diamond_image import DiamondImage
from app.models.diamond import Diamond
from app import db
from app.utils.decorators import token_required
import os
import uuid
from werkzeug.utils import secure_filename
from PIL import Image
import logging

# Create namespace
diamond_images_ns = Namespace('diamond-images', description='Diamond image management operations')

# Error model
error_model = diamond_images_ns.model('Error', {
    'message': fields.String(required=True, description='Error message')
})

# Diamond image model
diamond_image_model = diamond_images_ns.model('DiamondImage', {
    'id': fields.Integer(description='Image ID'),
    'diamond_id': fields.Integer(description='Diamond ID'),
    'image_url': fields.String(description='Image URL'),
    'image_type': fields.String(description='Image type'),
    'is_primary': fields.Boolean(description='Is primary image'),
    'alt_text': fields.String(description='Alt text'),
    'file_size': fields.Integer(description='File size in bytes'),
    'file_format': fields.String(description='File format'),
    'width': fields.Integer(description='Image width'),
    'height': fields.Integer(description='Image height'),
    'uploaded_at': fields.String(description='Upload timestamp'),
})

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_upload_folder():
    """Get the upload folder path, create if it doesn't exist"""
    upload_folder = os.path.join(current_app.root_path, '..', 'uploads', 'diamonds')
    os.makedirs(upload_folder, exist_ok=True)
    return upload_folder

@diamond_images_ns.route('/diamonds/<int:diamond_id>/images')
class DiamondImageList(Resource):
    @diamond_images_ns.doc('get_diamond_images')
    @diamond_images_ns.marshal_list_with(diamond_image_model)
    @diamond_images_ns.response(404, 'Diamond not found', error_model)
    @token_required
    def get(self, diamond_id):
        """Get all images for a diamond."""
        diamond = Diamond.query.get(diamond_id)
        if not diamond:
            diamond_images_ns.abort(404, 'Diamond not found')
        
        images = DiamondImage.query.filter_by(diamond_id=diamond_id).order_by(
            DiamondImage.is_primary.desc(),
            DiamondImage.uploaded_at.desc()
        ).all()
        
        return [image.to_dict() for image in images]
    
    @diamond_images_ns.doc('upload_diamond_image')
    @diamond_images_ns.response(201, 'Image uploaded successfully', diamond_image_model)
    @diamond_images_ns.response(400, 'Invalid request', error_model)
    @diamond_images_ns.response(404, 'Diamond not found', error_model)
    @token_required
    def post(self, diamond_id):
        """Upload an image for a diamond."""
        try:
            # Check if diamond exists
            diamond = Diamond.query.get(diamond_id)
            if not diamond:
                diamond_images_ns.abort(404, 'Diamond not found')
            
            # Check if file is in request
            if 'file' not in request.files:
                diamond_images_ns.abort(400, 'No file provided')
            
            file = request.files['file']
            if file.filename == '':
                diamond_images_ns.abort(400, 'No file selected')
            
            if not allowed_file(file.filename):
                diamond_images_ns.abort(400, f'File type not allowed. Allowed types: {", ".join(ALLOWED_EXTENSIONS)}')
            
            # Get additional parameters
            image_type = request.form.get('image_type', 'main')
            is_primary = request.form.get('is_primary', 'false').lower() == 'true'
            alt_text = request.form.get('alt_text', '')
            
            if image_type not in DiamondImage.get_valid_types():
                diamond_images_ns.abort(400, f'Invalid image type. Valid types: {", ".join(DiamondImage.get_valid_types())}')
            
            # Generate unique filename
            file_extension = secure_filename(file.filename).rsplit('.', 1)[1].lower()
            filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # Save file
            upload_folder = get_upload_folder()
            file_path = os.path.join(upload_folder, filename)
            file.save(file_path)
            
            # Get image dimensions and file info
            try:
                with Image.open(file_path) as img:
                    width, height = img.size
                    file_format = img.format.lower() if img.format else file_extension
            except Exception as e:
                logging.warning(f"Could not get image dimensions: {e}")
                width = height = None
                file_format = file_extension
            
            file_size = os.path.getsize(file_path)
            
            # If this is set as primary, unset other primary images
            if is_primary:
                DiamondImage.query.filter_by(diamond_id=diamond_id, is_primary=True).update({'is_primary': False})
            
            # Create database record
            image_url = f"/uploads/diamonds/{filename}"
            diamond_image = DiamondImage(
                diamond_id=diamond_id,
                image_url=image_url,
                image_type=image_type,
                is_primary=is_primary,
                alt_text=alt_text,
                file_size=file_size,
                file_format=file_format,
                width=width,
                height=height
            )
            
            db.session.add(diamond_image)
            db.session.commit()
            
            return diamond_image.to_dict(), 201

        except Exception as e:
            db.session.rollback()
            logging.error(f"Image upload failed: {str(e)}")
            diamond_images_ns.abort(500, f'Upload failed: {str(e)}')

@diamond_images_ns.route('/serve/<filename>')
class ServeUploadedImage(Resource):
    @diamond_images_ns.doc('serve_uploaded_image')
    @diamond_images_ns.response(200, 'Image file')
    @diamond_images_ns.response(404, 'Image not found')
    def get(self, filename):
        """Serve uploaded diamond image files."""
        try:
            upload_folder = get_upload_folder()
            return send_from_directory(upload_folder, filename)
        except Exception as e:
            logging.error(f"Failed to serve image {filename}: {str(e)}")
            diamond_images_ns.abort(404, 'Image not found')

@diamond_images_ns.route('/diamonds/<int:diamond_id>/images/<int:image_id>')
class DiamondImageDetail(Resource):
    @diamond_images_ns.doc('delete_diamond_image')
    @diamond_images_ns.response(200, 'Image deleted successfully')
    @diamond_images_ns.response(404, 'Image not found', error_model)
    @token_required
    def delete(self, diamond_id, image_id):
        """Delete a diamond image."""
        try:
            image = DiamondImage.query.filter_by(id=image_id, diamond_id=diamond_id).first()
            if not image:
                diamond_images_ns.abort(404, 'Image not found')
            
            # Delete file from filesystem
            upload_folder = get_upload_folder()
            filename = os.path.basename(image.image_url)
            file_path = os.path.join(upload_folder, filename)
            
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # Delete from database
            db.session.delete(image)
            db.session.commit()
            
            return {'message': 'Image deleted successfully'}, 200
            
        except Exception as e:
            db.session.rollback()
            logging.error(f"Image deletion failed: {str(e)}")
            diamond_images_ns.abort(500, f'Deletion failed: {str(e)}')
    
    @diamond_images_ns.doc('update_diamond_image')
    @diamond_images_ns.response(200, 'Image updated successfully', diamond_image_model)
    @diamond_images_ns.response(404, 'Image not found', error_model)
    @token_required
    def put(self, diamond_id, image_id):
        """Update diamond image metadata."""
        try:
            image = DiamondImage.query.filter_by(id=image_id, diamond_id=diamond_id).first()
            if not image:
                diamond_images_ns.abort(404, 'Image not found')
            
            data = request.get_json()
            
            # Update fields
            if 'image_type' in data:
                if data['image_type'] not in DiamondImage.get_valid_types():
                    diamond_images_ns.abort(400, f'Invalid image type. Valid types: {", ".join(DiamondImage.get_valid_types())}')
                image.image_type = data['image_type']
            
            if 'is_primary' in data:
                is_primary = data['is_primary']
                if is_primary and not image.is_primary:
                    # Unset other primary images
                    DiamondImage.query.filter_by(diamond_id=diamond_id, is_primary=True).update({'is_primary': False})
                image.is_primary = is_primary
            
            if 'alt_text' in data:
                image.alt_text = data['alt_text']
            
            db.session.commit()
            
            return image.to_dict(), 200
            
        except Exception as e:
            db.session.rollback()
            logging.error(f"Image update failed: {str(e)}")
            diamond_images_ns.abort(500, f'Update failed: {str(e)}')

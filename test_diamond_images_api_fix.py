#!/usr/bin/env python3
"""
Test the diamond images API file serving fix
"""

import requests

def test_diamond_images_api_fix():
    print("🔧 Testing Diamond Images API File Serving Fix")
    print("=" * 60)
    
    # Test the new diamond images API route for serving files
    filename = "c5274959daee4a85bb2764854f79f48f.png"
    url = f"http://localhost:8000/api/diamond-images/serve/{filename}"
    
    print(f"\n🖼️ Testing image serving:")
    print(f"   URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}", end="")
        
        if response.status_code == 200:
            print(" ✅")
            content_type = response.headers.get('content-type', 'unknown')
            content_length = len(response.content)
            print(f"   Content-Type: {content_type}")
            print(f"   Content-Length: {content_length} bytes")
            print("   ✅ Diamond images API file serving is working!")
            return True
        else:
            print(f" ❌")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_frontend_image_url():
    print("\n🔗 Testing Frontend Image URL Construction")
    print("=" * 60)
    
    # Simulate the frontend URL construction logic
    image_url = "/uploads/diamonds/c5274959daee4a85bb2764854f79f48f.png"
    
    print(f"   Original URL: {image_url}")
    
    # Frontend logic
    if image_url.startswith('/uploads/diamonds/'):
        filename = image_url.split('/').pop()
        full_url = f"http://localhost:8000/api/diamond-images/serve/{filename}"
    else:
        full_url = f"http://localhost:8000{image_url}"
    
    print(f"   Constructed URL: {full_url}")
    
    # Test the constructed URL
    try:
        response = requests.get(full_url, timeout=10)
        print(f"   Status: {response.status_code}", end="")
        
        if response.status_code == 200:
            print(" ✅")
            print("   ✅ Frontend URL construction is working!")
            return True
        else:
            print(f" ❌")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Diamond Images Fix")
    print("=" * 60)
    
    api_success = test_diamond_images_api_fix()
    frontend_success = test_frontend_image_url()
    
    print("\n" + "=" * 60)
    if api_success and frontend_success:
        print("🎉 ALL TESTS PASSED!")
        print("=" * 60)
        print("\n📝 What's Working:")
        print("   ✅ Diamond images API serves files correctly")
        print("   ✅ Frontend URL construction works")
        print("   ✅ Images should display in browser")
        print("\n🎯 Next Steps:")
        print("   1. Refresh your browser page")
        print("   2. Navigate to diamond edit page")
        print("   3. Images should now display correctly")
        print("   4. Upload functionality should work")
    else:
        print("❌ SOME TESTS FAILED")
        print("=" * 60)
        print("\n🔍 Issues:")
        if not api_success:
            print("   ❌ Diamond images API file serving not working")
        if not frontend_success:
            print("   ❌ Frontend URL construction not working")
        print("\n💡 Solutions:")
        print("   - Check if Flask server is running")
        print("   - Verify the uploaded file exists")
        print("   - Check server logs for errors")

#!/usr/bin/env python3
"""
Test upload and serve functionality
"""

import requests
import tempfile
import os
from PIL import Image

BASE_URL = "http://localhost:8000/api"

def create_test_image():
    """Create a simple test image"""
    img = Image.new('RGB', (100, 100), color='green')
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
    img.save(temp_file.name, 'PNG')
    temp_file.close()
    return temp_file.name

def test_upload_and_serve():
    print("🔧 Testing Upload and Serve Functionality")
    print("=" * 60)
    
    # Step 1: Login
    print("\n1. 🔐 Authentication")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shivam@109"
    })
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("   ✅ Authentication successful")
    
    # Step 2: Upload a new image
    print("\n2. ⬆️ Uploading new test image")
    test_image_path = create_test_image()
    
    try:
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test_image.png', f, 'image/png')}
            data = {
                'image_type': 'main',
                'is_primary': 'false',
                'alt_text': 'Test image for serving'
            }
            
            upload_response = requests.post(
                f"{BASE_URL}/diamond-images/diamonds/9/images",
                files=files,
                data=data,
                headers=headers
            )
            
        print(f"   Upload status: {upload_response.status_code}")
        
        if upload_response.status_code == 201:
            uploaded_image = upload_response.json()
            image_id = uploaded_image.get('id')
            image_url = uploaded_image.get('image_url')
            print(f"   ✅ Upload successful!")
            print(f"   Image ID: {image_id}")
            print(f"   Image URL: {image_url}")
            
            # Step 3: Check if file exists on filesystem
            print("\n3. 📁 Checking filesystem")
            filename = os.path.basename(image_url)
            upload_path = os.path.join('admin_backend', '..', 'uploads', 'diamonds', filename)
            abs_path = os.path.abspath(upload_path)
            print(f"   Expected file path: {abs_path}")
            
            if os.path.exists(abs_path):
                print(f"   ✅ File exists on filesystem!")
                print(f"   File size: {os.path.getsize(abs_path)} bytes")
                
                # Step 4: Test serving the file
                print("\n4. 🌐 Testing file serving")
                serve_url = f"{BASE_URL}/diamond-images/serve/{filename}"
                print(f"   Serve URL: {serve_url}")
                
                serve_response = requests.get(serve_url, headers=headers)
                print(f"   Serve status: {serve_response.status_code}")
                
                if serve_response.status_code == 200:
                    print("   ✅ File serving works!")
                    content_type = serve_response.headers.get('content-type', 'unknown')
                    content_length = len(serve_response.content)
                    print(f"   Content-Type: {content_type}")
                    print(f"   Content-Length: {content_length} bytes")
                    
                    # Step 5: Clean up - delete the test image
                    print("\n5. 🗑️ Cleaning up")
                    delete_response = requests.delete(
                        f"{BASE_URL}/diamond-images/diamonds/9/images/{image_id}",
                        headers=headers
                    )
                    print(f"   Delete status: {delete_response.status_code}")
                    
                    if delete_response.status_code in [200, 204]:
                        print("   ✅ Cleanup successful!")
                        return True
                    else:
                        print("   ⚠️ Cleanup failed, but upload/serve worked")
                        return True
                else:
                    print(f"   ❌ File serving failed: {serve_response.text[:200]}")
                    return False
            else:
                print("   ❌ File does not exist on filesystem!")
                print("   This indicates an upload issue")
                return False
        else:
            print(f"   ❌ Upload failed: {upload_response.text}")
            return False
            
    finally:
        # Clean up test image
        if os.path.exists(test_image_path):
            os.unlink(test_image_path)
    
    return False

if __name__ == "__main__":
    success = test_upload_and_serve()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 UPLOAD AND SERVE TEST PASSED!")
        print("=" * 60)
        print("\n📝 What's Working:")
        print("   ✅ Image upload saves files to filesystem")
        print("   ✅ Diamond images API serves files correctly")
        print("   ✅ Frontend should now display images")
        print("\n🎯 Next Steps:")
        print("   1. Refresh your browser page")
        print("   2. Images should now display correctly")
        print("   3. Upload new images should work")
    else:
        print("❌ UPLOAD AND SERVE TEST FAILED!")
        print("=" * 60)
        print("\n🔍 Possible Issues:")
        print("   - Upload directory permissions")
        print("   - Flask server file serving route")
        print("   - File path construction")
        print("\n💡 Try:")
        print("   - Check Flask server logs")
        print("   - Verify upload directory exists and is writable")
        print("   - Restart Flask server if needed")

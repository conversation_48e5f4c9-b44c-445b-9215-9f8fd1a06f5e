#!/usr/bin/env python3
"""
Test file serving with the correct file path
"""

import requests
import os

def test_file_serving():
    print("🔧 Testing File Serving with Correct Path")
    print("=" * 50)
    
    # Check if the file exists
    filename = "c5274959daee4a85bb2764854f79f48f.png"
    file_path = os.path.join("admin_backend", "uploads", "diamonds", filename)
    abs_path = os.path.abspath(file_path)
    
    print(f"\n📁 Checking file existence:")
    print(f"   File path: {abs_path}")
    
    if os.path.exists(abs_path):
        print("   ✅ File exists!")
        print(f"   File size: {os.path.getsize(abs_path)} bytes")
        
        # Test the API route
        print(f"\n🌐 Testing API file serving:")
        url = f"http://localhost:8000/api/diamond-images/serve/{filename}"
        print(f"   URL: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ File serving is working!")
                content_type = response.headers.get('content-type', 'unknown')
                content_length = len(response.content)
                print(f"   Content-Type: {content_type}")
                print(f"   Content-Length: {content_length} bytes")
                return True
            else:
                print(f"   ❌ Failed: {response.status_code}")
                if response.status_code == 404:
                    print("   Route not found - may need Flask server restart")
                else:
                    print(f"   Response: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return False
    else:
        print("   ❌ File does not exist!")
        return False

if __name__ == "__main__":
    success = test_file_serving()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 FILE SERVING IS WORKING!")
        print("=" * 50)
        print("\n📝 What this means:")
        print("   ✅ Uploaded files exist on filesystem")
        print("   ✅ API route serves files correctly")
        print("   ✅ Frontend should now display images")
        print("\n🎯 Next Steps:")
        print("   1. Refresh your browser page")
        print("   2. Navigate to diamond edit page")
        print("   3. Images should display correctly")
        print("   4. Upload functionality should work")
    else:
        print("❌ FILE SERVING NOT WORKING")
        print("=" * 50)
        print("\n🔍 Possible Issues:")
        print("   - Flask server needs restart for new route")
        print("   - Route registration issue")
        print("   - File permissions")
        print("\n💡 Solutions:")
        print("   1. Restart Flask server:")
        print("      cd admin_backend")
        print("      flask run --port=8000")
        print("   2. Check server logs for errors")
        print("   3. Verify route is registered")
